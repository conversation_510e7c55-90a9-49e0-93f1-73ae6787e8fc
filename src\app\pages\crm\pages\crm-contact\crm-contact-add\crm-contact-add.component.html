<div class="crm-contact">
  <div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
    <h4 class="header-title">{{ title }}</h4>
    <span *ngIf="isEditMode || isViewMode" class="created-by">
      <span class="bold-text">#{{ crmContactInfo?.firstName }}&nbsp;{{ crmContactInfo?.lastName }}</span>
      Created By <span class="bold-text">{{ crmContactInfo?.creator?.name }}</span> on
      {{ crmContactDetails?.createdDate | date : constants.fullDateFormat }}
    </span>
    <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
  </div>
  <form (ngSubmit)="onSubmit()">
    <div class="content">
      <section [formGroup]="contactFormGroup" class="contact-info">
        <div class="card p-3">
          <div class="title">
            <h4>Contact Information</h4>
          </div>
          <div class="row">
            <div class="col-12 gap-3">
              <label class="required">Contact type</label>
              <div class="d-flex gap-3">
                <div *ngFor="let type of contactType" class="field-checkbox">
                  <p-radioButton [inputId]="type.name" [value]="type.value" formControlName="isProspect" />
                  <label [for]="type.name" class="ms-2">
                    {{ type.name }}
                  </label>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label class="required">First Name</label>
              <input class="form-control" type="text" placeholder="Enter first name" formControlName="firstName" />
              <app-error-messages [control]="contactFormGroup.controls.firstName"></app-error-messages>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label class="required">Last Name</label>
              <input class="form-control" type="text" placeholder="Enter last name" formControlName="lastName" />
              <app-error-messages [control]="contactFormGroup.controls.lastName"></app-error-messages>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label>Company</label>
              <input class="form-control" type="text" placeholder="Enter company" formControlName="company" />
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label>Job Title</label>
              <input class="form-control" type="text" placeholder="Enter job title" formControlName="jobTitle" />
              <app-error-messages [control]="contactFormGroup.controls.jobTitle"></app-error-messages>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label>Department</label>
              <input class="form-control" type="text" placeholder="Enter department" formControlName="department" />
              <app-error-messages [control]="contactFormGroup.controls.department"></app-error-messages>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label class="required">Primary Email</label>
              <input class="form-control" type="email" placeholder="Enter email" formControlName="primaryEmail" />
              <app-error-messages [control]="contactFormGroup.controls.primaryEmail"></app-error-messages>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label>Secondary Email</label>
              <input class="form-control" type="email" placeholder="Enter email" formControlName="secondaryEmail" />
              <app-error-messages [control]="contactFormGroup.controls.secondaryEmail"></app-error-messages>
            </div>

            <div class="col-lg-3 col-md-6 col-12">
              <label>Website</label>
              <input class="form-control" type="text" placeholder="Enter website" formControlName="webSite" />
              <app-error-messages [control]="contactFormGroup.controls.webSite"></app-error-messages>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label>Fax</label>
              <input class="form-control" type="string" placeholder="Enter fax" formControlName="fax" />
              <app-error-messages [control]="contactFormGroup.controls.fax"></app-error-messages>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label class="required">Primary Phone</label>
              <input class="form-control" type="text" [mask]="contactNoFormat" placeholder="Enter mobile" formControlName="primaryPhone" />
              <app-error-messages [control]="contactFormGroup.controls.primaryPhone"></app-error-messages>
              <span class="text-danger f-s-12" *ngIf="contactFormGroup.controls.primaryPhone.errors?.mask">Please enter valid phone number</span>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label>Secondary Phone</label>
              <input class="form-control" type="text" [mask]="contactNoFormat" placeholder="Enter secondary phone" formControlName="secondaryPhone" />
              <app-error-messages [control]="contactFormGroup.controls.secondaryPhone"></app-error-messages>
              <span class="text-danger f-s-12" *ngIf="contactFormGroup.controls.secondaryPhone.errors?.mask">Please enter valid phone number</span>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
              <label>Account Rep</label>
              <p-dropdown
                appPreventClearFilter
                appendTo="body"
                [options]="AccountRepList"
                formControlName="accountReporterId"
                optionLabel="name"
                [showClear]="true"
                optionValue="id"
                [filter]="true"
                filterBy="stockNumber"
                placeholder="Select Account Reporter"
                [virtualScroll]="true"
                [itemSize]="30"
              >
                <ng-template pTemplate="empty">
                  <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.accountRep, data: AccountRepList }"></ng-container>
                </ng-template>
                <ng-template let-pipelineOwner pTemplate="item">
                  <span>{{ pipelineOwner.name }}</span>
                </ng-template>
              </p-dropdown>
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <label>Notes</label>
              <textarea placeholder="Enter notes" rows="3" formControlName="notes"></textarea>
              <div>
                <app-error-messages [control]="contactFormGroup.controls.notes"></app-error-messages>
              </div>
            </div>
          </div>
        </div>
        <div class="card p-3 mt-3">
          <div class="title m-t-20">
            <h4>Address Information</h4>
          </div>
          <div class="row">
            <div class="col-lg-6 col-12">
              <label class="required">Address</label>
              <input
                class="form-control"
                type="text"
                ngx-google-places-autocomplete
                ngx-gp-autocomplete
                [options]="options"
                (onAddressChange)="handleAddressChange($event)"
                placeholder="Enter address"
                formControlName="streetAddress"
              />
              <app-error-messages [control]="contactFormGroup.controls.streetAddress"></app-error-messages>
            </div>

            <div class="col-lg-2 col-md-6 col-12">
              <label class="required">City</label>
              <input class="form-control" type="text" placeholder="Enter city name" formControlName="city" />
              <app-error-messages [control]="contactFormGroup.controls.city"></app-error-messages>
            </div>
            <div class="col-lg-2 col-md-6 col-12">
              <label class="required">State</label>
              <input class="form-control" type="text" placeholder="Enter state name" formControlName="state" />
              <app-error-messages [control]="contactFormGroup.controls.state"></app-error-messages>
            </div>
            <div class="col-lg-2 col-md-6 col-12">
              <label class="required">Zip code</label>
              <input class="form-control" type="text" placeholder="Enter zip code" formControlName="zipcode" />
              <app-error-messages [control]="contactFormGroup.controls.zipcode"></app-error-messages>
            </div>
            <div class="col-lg-5 col-md-6 col-12">
              <label>County</label>
              <input class="form-control" type="text" placeholder="Enter County name" formControlName="county" />
              <app-error-messages [control]="contactFormGroup.controls.county"></app-error-messages>
            </div>
            <div class="col-lg-5 col-md-5 col-10">
              <label>Country</label>
              <input class="form-control" type="text" placeholder="Enter Country name" formControlName="country" />
              <app-error-messages [control]="contactFormGroup.controls.country"></app-error-messages>
            </div>
            <div class="col-1">
              <div
                class="add-shop map-icon"
                *ngIf="
                  contactFormGroup.get('streetAddress')?.value ||
                  contactFormGroup.get('city')?.value ||
                  contactFormGroup.get('state')?.value ||
                  contactFormGroup.get('country')?.value ||
                  contactFormGroup.get('zipcode')?.value
                "
              >
                <button class="btn btn-primary" type="button" (click)="toggleGoogleMapPopUp()">
                  <fa-icon [icon]="faIcons.faLocationDot"></fa-icon>
                </button>
              </div>
              <p-sidebar
                [closeOnEscape]="false"
                [dismissible]="false"
                [(visible)]="showGoogleMapSideBar"
                position="right"
                (onHide)="showGoogleMapSideBar = false"
                [blockScroll]="true"
                [showCloseIcon]="false"
                styleClass="p-sidebar-md"
                [baseZIndex]="10000"
                appendTo="body"
              >
                <app-google-map (onClose)="toggleGoogleMapPopUp()" *ngIf="showGoogleMapSideBar" [addressGroup]="newAddressFormGroup" [address]="fullAddress"> </app-google-map>
              </p-sidebar>
            </div>
          </div>
        </div>
      </section>
      <section *ngIf="isViewMode || isEditMode">
        <div class="add-wrapper">
          <p-tabView [(activeIndex)]="activeIndex" [scrollable]="true">
            <p-tabPanel header="Customer Lead">
              <app-crm-contact-customer-lead [crmContactInfo]="crmContactInfo"></app-crm-contact-customer-lead>
            </p-tabPanel>
            <p-tabPanel header="Quote">
              <app-crm-contact-quotation [crmContactInfo]="crmContactInfo"></app-crm-contact-quotation>
            </p-tabPanel>
            <p-tabPanel header="Task">
              <app-crm-contact-tasks [crmContactInfo]="crmContactInfo" [isViewMode]="isViewMode"></app-crm-contact-tasks>
            </p-tabPanel>
            <p-tabPanel header="Previously purchased">
              <app-crm-contact-previously-owned [crmContactInfo]="crmContactInfo"></app-crm-contact-previously-owned>
            </p-tabPanel>
            <p-tabPanel header="Reminders">
              <app-crm-contact-reminders
                [crmContactInfo]="crmContactInfo"
                [accountRepList]="AccountRepList"
                [customerId]="crmContactInfo!.id"
                [reminderFor]="contactFormGroup?.controls?.accountReporterId?.value"
              >
              </app-crm-contact-reminders>
            </p-tabPanel>
            <p-tabPanel header="Documents">
              <app-crm-contact-documents [crmContactId]="crmContactInfo!.id"></app-crm-contact-documents>
            </p-tabPanel>
          </p-tabView>
        </div>
      </section>
    </div>

    <div class="modal-footer">
      <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
      <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall *ngIf="!isViewMode">Save</button>
      <ng-container *appHasPermission="[permissionActions.UPDATE_CONTACTS]">
        <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall *ngIf="isViewMode && showEditBtn">Edit</button>
      </ng-container>
      <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!(isEditMode || isViewMode)" appShowLoaderOnApiCall>Save & Add New</button>
    </div>
  </form>
</div>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
