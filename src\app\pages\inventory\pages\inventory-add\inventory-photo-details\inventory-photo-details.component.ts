import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { InventoryPhotoItem, InventoryPhotoType } from '@pages/inventory/models';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-inventory-photo-details',
  templateUrl: './inventory-photo-details.component.html',
  styleUrls: ['./inventory-photo-details.component.scss']
})
export class InventoryPhotoDetailsComponent extends BaseComponent implements OnInit, OnChanges {
  title = "Photo details"
  hasDataBeenModified = false;
  photoDetailFormGroup!: FormGroup;
  isDisplayPicture = false;
  showDisplayPictureDiv = true;
  inventoryPhotoType = InventoryPhotoType;
  @Input() imageDetails!: InventoryPhotoItem | undefined;
  @Input() isDisplayPictureAssigned!: boolean;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() showNextImage: EventEmitter<InventoryPhotoItem> = new EventEmitter<InventoryPhotoItem>();
  @Output() showPreviousImage: EventEmitter<InventoryPhotoItem> = new EventEmitter<InventoryPhotoItem>();
  @Output() updateDetailsOfNewlyUploadedPhoto: EventEmitter<InventoryPhotoItem> = new EventEmitter<InventoryPhotoItem>();
  @Input() publicPhotos!: any[];
  @Input() internalPhotos!: any[];
  @Output() changePreviewImage: EventEmitter<InventoryPhotoItem> = new EventEmitter<InventoryPhotoItem>();
  constructor(private readonly fb: FormBuilder,
    private readonly inventoryService: InventoryService,
    private readonly toasterService: AppToasterService) {
    super();
  }

  ngOnInit(): void {
    this.isDisplayPicture = this.imageDetails?.displayPicture ? this.imageDetails?.displayPicture : false;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.imageDetails.currentValue) {
      this.initializeFormGroup();
      this.photoDetailFormGroup.get('detail')?.patchValue(this.imageDetails?.detail);
    }
  }

  private initializeFormGroup() {
    this.photoDetailFormGroup = this.fb.group({
      detail: new FormControl(null),
    });
    this.photoDetailFormGroup.get('detail')?.patchValue(this.imageDetails?.detail);
  }

  private save(): void {
    const imageId = this.imageDetails?.id ? this.imageDetails?.id.toString() : '0';
    const endpoint = API_URL_UTIL.inventory.photoDetail.replace(':unitImageId', imageId);
    if (this.imageDetails?.binary) {
      this.imageDetails.detail = this.photoDetailFormGroup.get('detail')?.value;
      this.updateDetailsOfNewlyUploadedPhoto.emit(this.imageDetails);
      this.onClose.emit(false);
      return;
    }
    this.inventoryService.patch(this.photoDescription, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.photoDetailsSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(this.hasDataBeenModified);
    });
  }

  get photoDescription(): FormGroup {
    return this.photoDetailFormGroup.get('detail')?.value;
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  onSubmit(): void {
    if (this.photoDetailFormGroup.invalid) {
      this.photoDetailFormGroup.markAllAsTouched();
      return;
    }
    if (this.photoDetailFormGroup.get('detail')?.value || this.imageDetails?.binary) {
      this.save();
    } else {
      this.toasterService.warning(MESSAGES.photoDetailsWarning);
    }

  }

  private saveDisplayPicture() {
    const imageId = this.imageDetails?.id ? this.imageDetails?.id.toString() : '0';
    const unitId = this.imageDetails?.unitId ? this.imageDetails?.unitId.toString() : '0';
    const endpoint = API_URL_UTIL.inventory.photoDisplay.replace(':imageId', imageId).replace(':unitId', unitId)
    this.inventoryService.patch('', endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.hasDataBeenModified = true;
      this.toasterService.success(MESSAGES.photoDisplayPicture);
    });
  }

  displayPictureToogle() {
    if (!this.imageDetails || this.imageDetails.photoType !== this.inventoryPhotoType.PUBLIC_PHOTO) {
      return;
    }

    // If trying to unset the display picture and it's the only public photo, prevent it
    if (this.imageDetails.displayPicture && this.publicPhotos.length <= 1) {
      this.toasterService.warning(MESSAGES.cannotUnselectOnlyPhoto);
      return;
    }

    // Toggle the display picture state
    this.imageDetails.displayPicture = !this.imageDetails.displayPicture;

    // If setting as display picture, save to backend immediately for existing images
    if (this.imageDetails.displayPicture && this.imageDetails.id && !this.imageDetails.binary) {
      this.saveDisplayPicture();
    }

    // Emit the change to parent component to handle state synchronization
    this.changePreviewImage.emit(this.imageDetails);
  }

  onNextImage(): void {
    this.showNextImage.emit(this.imageDetails)
  }

  shouldNextBtnDisable(): boolean {
    let shouldNextBtnDisable = false;
    if (this.imageDetails?.photoType === InventoryPhotoType.PUBLIC_PHOTO && this.publicPhotos?.length) {
      shouldNextBtnDisable = this.publicPhotos[this.publicPhotos?.length - 1]?.id !== this.imageDetails?.id ||
        this.publicPhotos[this.publicPhotos?.length - 1]?.tempId !== this.imageDetails?.tempId;
    }

    if (this.imageDetails?.photoType === InventoryPhotoType.INTERNAL_PHOTO && this.internalPhotos?.length) {
      shouldNextBtnDisable = this.internalPhotos[this.internalPhotos?.length - 1]?.id !== this.imageDetails?.id ||
        this.internalPhotos[this.internalPhotos?.length - 1]?.tempId !== this.imageDetails?.tempId;
    }
    return !shouldNextBtnDisable;
  }

  onPreviousImage(): void {
    this.showPreviousImage.emit(this.imageDetails);
  }

  shouldPreviousBtnDisable(): boolean {
    let shouldNextBtnDisable = false;
    if (this.imageDetails?.photoType === InventoryPhotoType.PUBLIC_PHOTO && this.publicPhotos?.length) {
      shouldNextBtnDisable = this.publicPhotos[0]?.id !== this.imageDetails?.id ||
        this.publicPhotos[0]?.tempId !== this.imageDetails?.tempId;
    }

    if (this.imageDetails?.photoType === InventoryPhotoType.INTERNAL_PHOTO && this.internalPhotos?.length) {
      shouldNextBtnDisable = this.internalPhotos[0]?.id !== this.imageDetails?.id ||
        this.internalPhotos[0]?.tempId !== this.imageDetails?.tempId;
    }
    return !shouldNextBtnDisable;
  }
}
